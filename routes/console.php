<?php

use App\Console\Commands\DownloadMaxMindDatabase;
use App\Models\Tag;

// Schedule MaxMind GeoLite2-Country database download every week
Schedule::command(DownloadMaxMindDatabase::class)->weekly();

Artisan::command('a', function () {
    //    $state = ['foo', 'bar', 'aut'];
    //
    //    // Get existing tag names
    //    $a = Tag::whereIn('name', $state)->pluck('name');
    //
    //    // Find which ones are missing
    //    $b = collect($state)->diff($a);
    //
    //    // Prepare insert as array of arrays
    //    $data = $b->map(fn ($name) => ['name' => $name])->all();
    //
    //    if (! empty($data)) {
    //        Tag::insert($data);
    //    }
    //
    //    dd($b);

    $tag = Tag::findOrFail(1);
    dd($tag->visits()->count());
});
