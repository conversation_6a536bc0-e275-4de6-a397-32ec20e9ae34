<?php

namespace App\Filament\Imports;

use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Number;

class UserImporter extends Importer
{
    protected static ?string $model = User::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('id')
                ->rules(['nullable', 'integer', 'min:1'])
                ->helperText('Optional: ID of existing user to update')
                ->example(1),

            ImportColumn::make('name')
                ->requiredMapping()
                ->rules(['required', 'string', 'max:255'])
                ->helperText('Full name of the user')
                ->example('<PERSON>'),

            ImportColumn::make('email')
                ->requiredMapping()
                ->rules(['required', 'email', 'max:255'])
                ->helperText('Email address (must be unique)')
                ->example('<EMAIL>'),

            ImportColumn::make('password')
                ->rules(['nullable', 'required_if:id,null', 'string', 'min:8', 'max:255'])
                ->sensitive()
                ->helperText('Password (required for new users, optional for updates)'),

            ImportColumn::make('is_active')
                ->boolean()
                ->rules(['nullable', 'boolean'])
                ->helperText('Whether the user account is active (default: true)')
                ->example('yes'),

            ImportColumn::make('is_super_admin')
                ->boolean()
                ->rules(['nullable', 'boolean'])
                ->helperText('Whether the user has super admin privileges (default: false)')
                ->example('no'),

            ImportColumn::make('roles')
                ->relationship(resolveUsing: 'name')
                ->multiple(';')
                ->helperText('Semicolon-separated role names (e.g., "admin;editor;viewer")')
                ->example('DevOps Team'),

            ImportColumn::make('permissions')
                ->relationship(resolveUsing: 'name')
                ->multiple(';')
                ->helperText('Semicolon-separated permission names (e.g., "create user;update link")')
                ->example('create user;update link'),
        ];
    }

    public function resolveRecord(): ?User
    {
        // If ID is provided and is a valid integer, try to find existing record
        if (! empty($this->data['id']) && is_numeric($this->data['id']) && $this->data['id'] > 0) {
            $existingUser = User::find($this->data['id']);
            if ($existingUser) {
                return $existingUser;
            }
        }

        // If email is provided, try to find by email (for updates)
        if (! empty($this->data['email'])) {
            $existingUser = User::where('email', $this->data['email'])->first();
            if ($existingUser) {
                return $existingUser;
            }
        }

        // Otherwise create new record
        return new User;
    }

    protected function beforeFill(): void
    {
        // Remove ID from data if it's empty or null to prevent constraint violations
        if (empty($this->data['id'])) {
            unset($this->data['id']);
        }

        // Set default values for boolean fields if not provided
        if (! isset($this->data['is_active'])) {
            $this->data['is_active'] = true;
        }

        if (! isset($this->data['is_super_admin'])) {
            $this->data['is_super_admin'] = false;
        }
    }

    //    protected function beforeValidate(): void
    //    {
    //                // For new users, password is required
    //                $isNewUser = empty($this->data['id']) && (empty($this->data['email']) || ! User::where('email', $this->data['email'])->exists());
    //
    //                if ($isNewUser && empty($this->data['password'])) {
    //                    throw new \Exception('Password is required for new users.');
    //                }
    //    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your user import has completed and '.Number::format($import->successful_rows).' '.str('row')->plural($import->successful_rows).' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' '.Number::format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to import.';
        }

        return $body;
    }
}
